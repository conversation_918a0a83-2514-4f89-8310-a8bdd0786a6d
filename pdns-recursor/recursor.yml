incoming:
  # Allow queries only from dnsdist (and other containers on the same Docker network).
  allow_from: [ '**********/12', '***********/16' ]
  # Listen on all interfaces within the container.
  listen: [ '0.0.0.0', '::' ]

recursor:
  # Forward queries for 'service.internal' to the authoritative server using static IP.
  forward_zones:
    - zone: 'service.internal.'
      forwarders:
        - '***********:53'
